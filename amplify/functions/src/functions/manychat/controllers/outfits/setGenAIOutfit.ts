import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { manychatOutfitService } from "../../../../services/manychat";
import { ManychatStyleProfile } from "../../../../types/style.types";
import { ManychatSituationalContext } from "../../../../types/manychat";
import { isValidManychatValue } from "../../utils/validation";

export const setGenAIOutfit = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  try {
    if (!event.body) {
      return {
        statusCode: 400,
        body: JSON.stringify({ error: "Request body is required" }),
      };
    }

    const requestBody = JSON.parse(event.body);
    const { styleProfile, context, userId } = requestBody;

    if (!styleProfile || !context || !userId) {
      return {
        statusCode: 400,
        body: JSON.stringify({ error: "styleProfile, context, and userId are required" }),
      };
    }

    // Validate the style profile
    const validatedStyleProfile = styleProfile as ManychatStyleProfile;

    // Validate the context and filter out invalid values
    const validatedContext: ManychatSituationalContext = {
      occasion: context.occasion,
      stylePreference: context.stylePreference,
    };

    // Only include location and weather if they are valid values
    if (isValidManychatValue(context.location)) {
      validatedContext.location = context.location;
    } else {
      console.log(`[setGenAIOutfit] Skipping invalid location value: "${context.location}"`);
    }

    if (isValidManychatValue(context.feelsLikeWeather)) {
      validatedContext.feelsLikeWeather = context.feelsLikeWeather;
    } else {
      console.log(`[setGenAIOutfit] Skipping invalid weather value: "${context.feelsLikeWeather}"`);
    }

    // Include outfit iteration parameters if provided
    if (context.suggestedOutfit && isValidManychatValue(context.suggestedOutfit)) {
      validatedContext.suggestedOutfit = context.suggestedOutfit;
      console.log(`[setGenAIOutfit] Including suggested outfit for iteration`);
    }

    if (context.userOverride && isValidManychatValue(context.userOverride)) {
      validatedContext.userOverride = context.userOverride;
      console.log(`[setGenAIOutfit] Including user override for outfit refinement`);
    }

    // Call the service method with the filtered context
    await manychatOutfitService.setGenAIOutfit(validatedStyleProfile, validatedContext, userId);

    return {
      statusCode: 200,
      body: JSON.stringify({ message: "Outfit generated successfully" }),
    };
  } catch (error) {
    console.error("Error in getGenaiOutfit controller:", error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: "Internal server error" }),
    };
  }
};